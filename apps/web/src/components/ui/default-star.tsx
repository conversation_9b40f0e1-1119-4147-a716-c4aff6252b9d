import {
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Star } from "lucide-react";

export function DefaultStar({ className }: { className?: string }) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Star className={className} />
        </TooltipTrigger>
        <TooltipContent>
          <p>Default agent</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
