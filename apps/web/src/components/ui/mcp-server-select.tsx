import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useMCPContext } from '@/providers/MCP';

export function MCPServerSelect() {
  const { servers, selectedServer, selectServer, loading } = useMCPContext();

  if (servers.length <= 1) return null;

  return (
    <Select
      value={selectedServer || servers[0]?.id}
      onValueChange={selectServer}
      disabled={loading}
    >
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="Select MCP Server" />
      </SelectTrigger>
      <SelectContent>
        {servers.map((server) => (
          <SelectItem key={server.id} value={server.id}>
            {server.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
