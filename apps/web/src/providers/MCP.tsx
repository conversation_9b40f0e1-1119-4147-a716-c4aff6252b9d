import React, {
  createContext,
  useContext,
  PropsWithChildren,
  useEffect,
  useState,
} from "react";
import useMCP from "../hooks/use-mcp";
import { useMCPServers } from "../hooks/use-mcp-servers";

type MCPContextType = ReturnType<typeof useMCP> & {
  loading: boolean;
  error: Error | null;
  servers: ReturnType<typeof useMCPServers>["servers"];
  selectedServer: string | null;
  selectServer: (serverId: string) => void;
};

const MCPContext = createContext<MCPContextType | null>(null);

export const MCPProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { servers, selectedServer, selectServer } = useMCPServers();
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(false);

  const mcpState = useMCP({
    name: "Tools Interface",
    version: "1.0.0",
    serverId: selectedServer || undefined,
  });

  // Extract functions to avoid including the entire mcpState object in dependencies
  const { getTools, setTools } = mcpState;

  useEffect(() => {
    let mounted = true;

    const loadTools = async () => {
      if (!mounted) return;
      setLoading(true);
      setError(null);

      console.log("MCPProvider: Loading tools for server:", selectedServer);

      try {
        if (!selectedServer) {
          console.log("MCPProvider: No server selected, clearing tools");
          setTools([]);
          return;
        }

        // Validate server exists in available servers
        const serverExists = servers.some(server => server.id === selectedServer);
        if (!serverExists) {
          throw new Error("Selected MCP server not found");
        }

        console.log("MCPProvider: Fetching tools from server:", selectedServer);
        const tools = await getTools();
        console.log("MCPProvider: Received tools:", tools.length);
        if (mounted) {
          setTools(tools);
        }
      } catch (error) {
        console.error("Failed to load tools:", error);
        if (mounted) {
          setError(error instanceof Error ? error : new Error('Failed to load tools'));
          setTools([]);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    loadTools();

    return () => {
      mounted = false;
    };
  }, [selectedServer, getTools, setTools, servers]);

  return (
    <MCPContext.Provider
      value={{
        ...mcpState,
        loading,
        error,
        servers,
        selectedServer,
        selectServer,
      }}
    >
      {children}
    </MCPContext.Provider>
  );
};

export const useMCPContext = () => {
  const context = useContext(MCPContext);
  if (context === null) {
    throw new Error("useMCPContext must be used within a MCPProvider");
  }
  return context;
};
