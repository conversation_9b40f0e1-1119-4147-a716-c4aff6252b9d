import {
  ConfigurableFieldAgentsMetadata,
  ConfigurableFieldMCPMetadata,
  ConfigurableFieldRAGMetadata,
  ConfigurableFieldUIMetadata,
} from "@/types/configurable";
import { useCallback, useState } from "react";
import { useAgents } from "./use-agents";
import {
  extractConfigurationsFromAgent,
  getConfigurableDefaults,
} from "@/lib/ui-config";
import { useConfigStore } from "@/features/chat/hooks/use-config-store";
import { Agent } from "@/types/agent";
import { useQueryState } from "nuqs";

/**
 * A custom hook for managing and accessing the configurable
 * fields on an agent.
 */
export function useAgentConfig() {
  const { getAgentConfigSchema } = useAgents();
  const [chatWithCollectionId, setChatWithCollectionId] = useQueryState(
    "chatWithCollectionId",
  );

  const [configurations, setConfigurations] = useState<
    ConfigurableFieldUIMetadata[]
  >([]);
  const [toolConfigurations, setToolConfigurations] = useState<
    ConfigurableFieldMCPMetadata[]
  >([]);
  const [ragConfigurations, setRagConfigurations] = useState<
    ConfigurableFieldRAGMetadata[]
  >([]);
  const [agentsConfigurations, setAgentsConfigurations] = useState<
    ConfigurableFieldAgentsMetadata[]
  >([]);

  const [supportedConfigs, setSupportedConfigs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const clearState = useCallback(() => {
    setConfigurations([]);
    setToolConfigurations([]);
    setRagConfigurations([]);
    setAgentsConfigurations([]);
    setLoading(false);
  }, []);

  const validateToolConfig = useCallback((toolConfig: ConfigurableFieldMCPMetadata[]) => {
    if (!toolConfig.length) return toolConfig;

    // Get the first available MCP server URL from the array as fallback
    const mcpServerUrls = process.env.NEXT_PUBLIC_MCP_SERVER_URLS
      ? JSON.parse(process.env.NEXT_PUBLIC_MCP_SERVER_URLS)
      : [];

    const fallbackMcpServerUrl = Array.isArray(mcpServerUrls) && mcpServerUrls.length > 0
      ? mcpServerUrls[0]
      : null;

    // Ensure URL is present in the default configuration
    return toolConfig.map(config => ({
      ...config,
      default: {
        ...config.default,
        url: config.default?.url || fallbackMcpServerUrl,
        auth_required: process.env.NEXT_PUBLIC_MCP_AUTH_REQUIRED === "true",
      },
    }));
  }, []);

  const getSchemaAndUpdateConfig = useCallback(
    async (
      agent: Agent,
    ): Promise<{
      name: string;
      description: string;
      config: Record<string, any>;
    }> => {
      clearState();
      setLoading(true);
      
      try {
        const schema = await getAgentConfigSchema(
          agent.assistant_id,
          agent.deploymentId,
        );

        if (!schema) {
          return {
            name: agent.name,
            description: (agent.metadata?.description as string | undefined) ?? "",
            config: {},
          };
        }

        const { configFields, toolConfig, ragConfig, agentsConfig } =
          extractConfigurationsFromAgent({
            agent,
            schema,
          });

        const agentId = agent.assistant_id;
        const { setDefaultConfig } = useConfigStore.getState();
        setConfigurations(configFields);
        setDefaultConfig(agentId, configFields);

        const supportedConfigs: string[] = [];

        // Handle tool configurations with validation
        if (toolConfig.length) {
          const validatedToolConfig = validateToolConfig(toolConfig);
          setDefaultConfig(`${agentId}:selected-tools`, validatedToolConfig);
          setToolConfigurations(validatedToolConfig);
          supportedConfigs.push("tools");
        }

        // Handle RAG configurations
        if (ragConfig.length) {
          if (chatWithCollectionId) {
            ragConfig[0].default = {
              ...ragConfig[0].default,
              collections: [chatWithCollectionId],
            };
            setChatWithCollectionId(null);
          }
          setDefaultConfig(`${agentId}:rag`, ragConfig);
          setRagConfigurations(ragConfig);
          supportedConfigs.push("rag");
        }

        // Handle agent configurations
        if (agentsConfig.length) {
          setDefaultConfig(`${agentId}:agents`, agentsConfig);
          setAgentsConfigurations(agentsConfig);
          supportedConfigs.push("supervisor");
        }

        setSupportedConfigs(supportedConfigs);

        const configurableDefaults = getConfigurableDefaults(
          configFields,
          toolConfig,
          ragConfig,
          agentsConfig,
        );

        return {
          name: agent.name,
          description:
            (agent.metadata?.description as string | undefined) ?? "",
          config: configurableDefaults,
        };
      } finally {
        setLoading(false);
      }
    },
    [clearState, getAgentConfigSchema],
  );

  return {
    clearState,
    getSchemaAndUpdateConfig,

    configurations,
    toolConfigurations,
    ragConfigurations,
    agentsConfigurations,
    supportedConfigs,

    loading,
  };
}
