import { useState, useCallback, useEffect } from 'react';

export interface MCPServer {
  url: string;
  id: string;
  name: string;
}

const LAST_SELECTED_SERVER_KEY = 'lastSelectedMCPServer';

export function useMCPServers() {
  const [selectedServer, setSelectedServer] = useState<string | null>(() => {
    // Try to get last selected server from localStorage during initialization
    if (typeof window !== 'undefined') {
      return localStorage.getItem(LAST_SELECTED_SERVER_KEY);
    }
    return null;
  });
  const [servers, setServers] = useState<MCPServer[]>([]);

  // Initialize servers and handle default selection
  useEffect(() => {
    const serverUrls = process.env.NEXT_PUBLIC_MCP_SERVER_URLS
      ? JSON.parse(process.env.NEXT_PUBLIC_MCP_SERVER_URLS)
      : [];

    const mcpServers = serverUrls.map((url: string) => {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      const id = pathParts[pathParts.length - 1] || '';

      // Create a more descriptive name based on the hostname
      let name = `MCP Server (${id})`;
      if (urlObj.hostname.includes('composio')) {
        name = `Composio Server (${id.substring(0, 8)}...)`;
      } else if (urlObj.hostname.includes('arcade')) {
        name = `Arcade Server (${id})`;
      }

      return {
        url,
        id,
        name,
      };
    });

    setServers(mcpServers);

    // Set default server if none is selected
    if (mcpServers.length > 0 && !selectedServer) {
      const defaultServer = mcpServers[0].id;
      setSelectedServer(defaultServer);
      if (typeof window !== 'undefined') {
        localStorage.setItem(LAST_SELECTED_SERVER_KEY, defaultServer);
      }
    }
  }, []); // Remove selectedServer dependency

  const selectServer = useCallback((serverId: string) => {
    setSelectedServer(serverId);
    // Save selection to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem(LAST_SELECTED_SERVER_KEY, serverId);
    }
  }, []);

  return {
    servers,
    selectedServer,
    selectServer,
  };
}
