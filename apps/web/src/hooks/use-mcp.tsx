import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { Tool } from "@/types/tool";
import { useState, useCallback, useEffect } from "react";

/**
 * Custom hook for interacting with the Model Context Protocol (MCP).
 * Provides functions to connect to an MCP server and list available tools.
 */
export default function useMCP({
  name,
  version,
  serverId,
}: {
  name: string;
  version: string;
  serverId?: string;
}) {
  const [tools, setTools] = useState<Tool[]>([]);
  const [cursor, setCursor] = useState("");
  const [client, setClient] = useState<Client | null>(null);
  const [connectPromise, setConnectPromise] = useState<Promise<Client> | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Reset state when server changes
  useEffect(() => {
    setClient(null);
    setConnectPromise(null);
    setTools([]);
    setCursor("");
    setError(null);
  }, [serverId]);

  /**
   * Creates and validates an MCP client connection
   */
  const createAndConnectMCPClient = useCallback(async (forceRecreate: boolean = false) => {
    if (!process.env.NEXT_PUBLIC_BASE_API_URL) {
      const error = new Error("NEXT_PUBLIC_BASE_API_URL is not defined");
      setError(error);
      throw error;
    }

    if (!serverId) {
      const error = new Error("Cannot create MCP client without a server ID");
      setError(error);
      throw error;
    }

    // Clear existing state if forcing recreate
    if (forceRecreate) {
      setClient(null);
      setConnectPromise(null);
      setTools([]);
      setCursor("");
    }

    try {
      // Return existing connection promise if valid
      if (!forceRecreate && connectPromise) {
        const existingClient = await connectPromise;
        if (existingClient) {
          return existingClient;
        }
      }

      const url = new URL(`${process.env.NEXT_PUBLIC_BASE_API_URL}/oap_mcp`);
      url.searchParams.set("serverId", serverId);

      // Create a new connection promise
      const newPromise = (async () => {
        try {
          const connectionClient = new StreamableHTTPClientTransport(url);
          const newClient = new Client({
            name,
            version,
          });

          await newClient.connect(connectionClient);
          setClient(newClient);
          setError(null);
          return newClient;
        } catch (err) {
          const error = err instanceof Error ? err : new Error(String(err));
          setError(error);
          setConnectPromise(null);
          setClient(null);
          throw error;
        }
      })();

      setConnectPromise(newPromise);
      return newPromise;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      setConnectPromise(null);
      setClient(null);
      throw error;
    }
  }, [serverId, name, version]);

  /**
   * Gets available tools from the MCP server
   */
  const getTools = useCallback(async (nextCursor?: string): Promise<Tool[]> => {
    if (!serverId) {
      setTools([]);
      const error = new Error("Cannot fetch tools without a server ID");
      setError(error);
      throw error;
    }

    try {
      const mcp = await createAndConnectMCPClient(nextCursor ? false : true);
      const toolsResponse = await mcp.listTools({ cursor: nextCursor });

      // Debug logging
      console.log("MCP Tools Response:", toolsResponse);
      console.log("Tools count:", toolsResponse.tools?.length || 0);

      if (toolsResponse.nextCursor) {
        setCursor(toolsResponse.nextCursor);
      } else {
        setCursor("");
      }

      // Filter out duplicates by name
      const uniqueTools = toolsResponse.tools.filter((tool, index, self) =>
        index === self.findIndex((t) => t.name === tool.name)
      );

      console.log("Unique tools count:", uniqueTools.length);
      setError(null);
      return uniqueTools;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      setCursor("");
      setConnectPromise(null);
      setClient(null);
      setTools([]);
      throw error;
    }
  }, [serverId, createAndConnectMCPClient]);

  /**
   * Calls a specific tool on the MCP server
   */
  const callTool = useCallback(async ({
    name: toolName,
    args,
    version: toolVersion,
  }: {
    name: string;
    args: Record<string, any>;
    version?: string;
  }) => {
    if (!serverId) {
      const error = new Error("Cannot call tool without a server ID");
      setError(error);
      throw error;
    }

    try {
      // Always force a new client connection when calling a tool
      const mcp = await createAndConnectMCPClient(true);
      console.log(`Calling tool ${toolName} on server ${serverId}`);
      
      const response = await mcp.callTool({
        name: toolName,
        version: toolVersion,
        arguments: args,
      });
      
      console.log(`Tool ${toolName} response received:`, response);
      setError(null);
      return response;
    } catch (err) {
      console.error(`Error calling tool ${toolName}:`, err);
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      setConnectPromise(null);
      setClient(null);
      throw error;
    }
  }, [serverId, createAndConnectMCPClient]);

  // Cleanup and reconnection effect
  useEffect(() => {
    const cleanup = () => {
      setClient(null);
      setConnectPromise(null);
      setTools([]);
      setCursor("");
      setError(null);
    };

    cleanup(); // Clean up when serverId changes

    // Don't include createAndConnectMCPClient in dependencies to avoid circular dependency
    // The function will be called when needed by getTools or callTool

    return cleanup;
  }, [serverId]);

  // Memoize setTools to prevent unnecessary re-renders
  const memoizedSetTools = useCallback((newTools: Tool[] | ((prevTools: Tool[]) => Tool[])) => {
    setTools(newTools);
  }, []);

  return {
    tools,
    setTools: memoizedSetTools,
    cursor,
    error,
    getTools,
    callTool,
    createAndConnectMCPClient,
  };
}
