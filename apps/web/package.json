{"name": "@open-agent-platform/web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "turbo build:internal --filter=@open-agent-platform/web", "build:internal": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@langchain/core": "^0.3.44", "@langchain/langgraph-sdk": "^0.0.76", "@modelcontextprotocol/sdk": "^1.11.4", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "1.1.2", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-toast": "1.1.0", "@radix-ui/react-tooltip": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "4.1.0", "framer-motion": "^12.7.3", "katex": "latest", "langgraph-nextjs-api-passthrough": "^0.1.0", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "next-themes": "^0.4.6", "nuqs": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.2", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.0", "uuid": "^11.1.0", "zod": "3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.13", "@types/lodash": "^4.17.16", "@types/node": "22.15.2", "@types/react": "^19.0.8", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "10.0.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.19.0", "eslint-config-next": "15.2.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "next": "^15.3.1", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-scrollbar": "3.1.0", "tailwindcss": "^4.0.13", "turbo": "^2.5.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}