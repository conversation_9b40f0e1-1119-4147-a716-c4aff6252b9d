---
title: 'Introduction'
description: 'An introduction to Open Agent Platform'
---

Open Agent Platform is a citizen developer platform, allowing non-technical users to build, prototype, and use agents. These agents can be connected to a wide range of tools, RAG servers, and even other agents through an Agent Supervisor!

<CardGroup cols={2}>
  <Card
    title="Quick Start"
    icon="bolt"
    href="/quickstart"
  >
    Get your Open Agent Platform running quickly
  </Card>
  <Card
    title="Agent Setup"
    icon="robot"
    href="/setup/agents"
  >
    Deploy and configure your agents
  </Card>
  <Card
    title="RAG Integration"
    icon="database"
    href="/setup/rag-server"
  >
    Connect your agents to knowledge bases
  </Card>
  <Card
    title="MCP Server"
    icon="toolbox"
    href="/setup/mcp-server"
  >
    Extend your agents with powerful tools
  </Card>
</CardGroup>

## What is Open Agent Platform?

Open Agent Platform provides a modern, web-based interface for creating, managing, and interacting with LangGraph agents. It's designed with simplicity in mind, making it accessible to users without technical expertise, while still offering advanced capabilities for developers.

## Key Features

- **Agent Management**: Build, configure, and interact with agents through an intuitive interface
- **RAG Integration**: First-class support for Retrieval Augmented Generation with [LangConnect](https://github.com/langchain-ai/langconnect)
- **MCP Tools**: Connect your agents to external tools through MCP servers
- **Agent Supervision**: Orchestrate multiple agents working together through an Agent Supervisor
- **Authentication**: Built-in authentication and access control

## Getting Started

Follow our [quickstart guide](/quickstart) to set up your own instance of Open Agent Platform.
