---
title: MCP Server
---
<Tip>
  Open Agent Platform only supports connecting to MCP servers which support Streamable HTTP requests. As of **05/10/2025**, there aren't many MCP servers which support this, which is why we've released the demo application connected to [Arcade's](https://arcade-ai.com/) MCP server.
</Tip>

Open Agent Platform was built with first class support for connecting agents to MCP servers which support Streamable HTTP requests. You can configure your MCP servers using the `NEXT_PUBLIC_MCP_SERVER_URLS` environment variable inside the `apps/web/` directory.

The environment variable should be set to a JSON array of MCP server URLs. Ensure the URLs do *not* end in `/mcp`, as the OAP web app will append this to the URLs when making requests to the MCP servers.

E.g. if your MCP server URLs are:

```bash
https://api.arcade.dev/v1/mcps/ms_0ujssxh0cECutqzMgbtXSGnjorm/mcp
https://api.arcade.dev/v1/mcps/ms_1vjssxh0cECutqzMgbtXSGnjorm/mcp
```

You should set the environment variable as:

```bash
NEXT_PUBLIC_MCP_SERVER_URLS='[
  "https://api.arcade.dev/v1/mcps/ms_0ujssxh0cECutqzMgbtXSGnjorm",
  "https://api.arcade.dev/v1/mcps/ms_1vjssxh0cECutqzMgbtXSGnjorm"
]'
```

### Authenticated Servers

To connect to MCP servers which require authentication, set `NEXT_PUBLIC_MCP_AUTH_REQUIRED=true`. The way MCP authentication works is as follows:

The web client makes a request to the proxy route (`/api/oap_mcp`). Inside this API route, we use Supabase's JWT to authenticate with the MCP server. This means you must implement an endpoint on your MCP servers which allows for exchanging a Supabase JWT (or any other JWT if you choose to use a different authentication provider) for an MCP access token. This access token is then used to authenticate requests to the MCP server. After this exchange, we use the MCP access token to make requests to the MCP server on behalf of the user, passing it through the `Authorization` header of the request. When sending the request response back to the client, we include the MCP access token in the `x-mcp-access-token` header of the response. This allows the client to use the MCP access token in future requests to the MCP server, without having to authenticate with the MCP server each time. It's set to expire after one hour by default.

Each URL in `NEXT_PUBLIC_MCP_SERVER_URLS` must be formatted so that the proxy route can append `/mcp` at the end to make requests to the MCP server, and `/oauth/token` at the end to make requests to the MCP server's OAuth token endpoint.

Optionally, you can set the `MCP_TOKENS` environment variable to contain an object with an `access_token` field. If this environment variable is set, we will attempt to use that access token to authenticate requests to the MCP servers. This is useful for testing, or if you want to use a different authentication provider than Supabase.

### Unauthenticated Servers

To connect to MCP servers which do not require authentication, just set the `NEXT_PUBLIC_MCP_SERVER_URLS` environment variable with your MCP server URLs. If `NEXT_PUBLIC_MCP_AUTH_REQUIRED` is not set or not set to `true`, we will not execute the OAuth request in the proxy route, and instead directly call your MCP servers.

### Changing MCP Server URLs

If you change your MCP server URLs, you'll need to update all of your agents to use the new URLs. We've included a script in this repo to do just that. This script can be found in [`apps/web/scripts/update-agents-mcp-url.ts`](https://github.com/langchain-ai/open-agent-platform/blob/main/apps/web/scripts/update-agents-mcp-url.ts).

To update your agents' MCP server URLs, ensure the latest URLs are set under the environment variable `NEXT_PUBLIC_MCP_SERVER_URLS`, along with your deployments under `NEXT_PUBLIC_DEPLOYMENTS`, and a LangSmith API key under `LANGSMITH_API_KEY` (this is because the script uses LangSmith auth to authenticate with your LangGraph server, bypassing any user authentication). Then, run the script:

```bash
# Ensure you're inside the `apps/web` directory
cd apps/web

# Run the script via TSX.
npx tsx scripts/update-agents-mcp-url.ts
```