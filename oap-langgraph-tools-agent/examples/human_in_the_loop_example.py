#!/usr/bin/env python3
"""
Example script demonstrating human-in-the-loop functionality with LangGraph Tools Agent.

This script shows how to:
1. Configure the agent with HIL enabled
2. Run the agent with tool calls that require human approval
3. <PERSON>le interrupts and resume execution with human input

Prerequisites:
- Set up Neo4j environment variables (NEO4J_URI, NEO4J_USERNAME, NEO4J_PASSWORD)
- Ensure the agent is running with proper authentication
"""

import asyncio
import os
from langgraph.types import Command
from tools_agent.agent import graph


async def example_neo4j_hil():
    """Example of using HIL with Neo4j write operations."""
    
    print("=== Neo4j Human-in-the-Loop Example ===")
    
    # Configuration with HIL enabled for Neo4j write operations
    config = {
        "configurable": {
            "thread_id": "hil_example_1",
            "model_name": "openai:gpt-4.1-nano",
            "temperature": 0.7,
            "graph_database_tools": True,
            "human_in_the_loop": True,
            "hil_neo4j_write_ops": True,
            "hil_all_tools": False,
        }
    }
    
    # Create the agent
    agent = await graph(config)
    
    # Test message that will trigger a Neo4j write operation
    messages = [{
        "role": "user", 
        "content": "Create a new person node in <PERSON>4j with name '<PERSON>' and age 30"
    }]
    
    print("\n1. Starting agent with write operation request...")
    print("   This should trigger a human-in-the-loop interrupt.")
    
    # Run the agent - this should pause at the interrupt
    try:
        async for chunk in agent.astream({"messages": messages}, config):
            print(f"Chunk: {chunk}")
            
            # Check if we hit an interrupt
            if "interrupt" in str(chunk).lower():
                print("\n2. Agent paused for human review!")
                print("   In a real application, you would present the tool call to a human.")
                print("   For this example, we'll automatically approve it.")
                break
    except Exception as e:
        print(f"Expected interrupt occurred: {e}")
    
    print("\n3. Resuming with human approval...")
    
    # Resume with approval
    resume_command = Command(resume=[{"type": "accept"}])
    
    try:
        async for chunk in agent.astream(resume_command, config):
            print(f"Resume chunk: {chunk}")
    except Exception as e:
        print(f"Resume completed or error: {e}")


async def example_all_tools_hil():
    """Example of using HIL with all tools enabled."""
    
    print("\n=== All Tools Human-in-the-Loop Example ===")
    
    # Configuration with HIL enabled for all tools
    config = {
        "configurable": {
            "thread_id": "hil_example_2",
            "model_name": "openai:gpt-4.1-nano",
            "temperature": 0.7,
            "graph_database_tools": True,
            "human_in_the_loop": True,
            "hil_neo4j_write_ops": True,
            "hil_all_tools": True,  # This will require approval for ALL tools
        }
    }
    
    # Create the agent
    agent = await graph(config)
    
    # Test message that will trigger any tool operation
    messages = [{
        "role": "user", 
        "content": "What's the current schema of the Neo4j database?"
    }]
    
    print("\n1. Starting agent with schema query (read operation)...")
    print("   With hil_all_tools=True, even read operations require approval.")
    
    # Run the agent - this should pause at the interrupt
    try:
        async for chunk in agent.astream({"messages": messages}, config):
            print(f"Chunk: {chunk}")
            
            # Check if we hit an interrupt
            if "interrupt" in str(chunk).lower():
                print("\n2. Agent paused for human review of read operation!")
                break
    except Exception as e:
        print(f"Expected interrupt occurred: {e}")
    
    print("\n3. Resuming with human approval...")
    
    # Resume with approval
    resume_command = Command(resume=[{"type": "accept"}])
    
    try:
        async for chunk in agent.astream(resume_command, config):
            print(f"Resume chunk: {chunk}")
    except Exception as e:
        print(f"Resume completed or error: {e}")


async def example_edit_tool_call():
    """Example of editing a tool call during HIL."""
    
    print("\n=== Edit Tool Call Example ===")
    
    config = {
        "configurable": {
            "thread_id": "hil_example_3",
            "model_name": "openai:gpt-4.1-nano",
            "temperature": 0.7,
            "graph_database_tools": True,
            "human_in_the_loop": True,
            "hil_neo4j_write_ops": True,
            "hil_all_tools": False,
        }
    }
    
    agent = await graph(config)
    
    messages = [{
        "role": "user", 
        "content": "Create a person named 'Bob' with age 25"
    }]
    
    print("\n1. Starting agent with create operation...")
    
    try:
        async for chunk in agent.astream({"messages": messages}, config):
            print(f"Chunk: {chunk}")
            if "interrupt" in str(chunk).lower():
                print("\n2. Agent paused - we'll edit the tool call!")
                break
    except Exception as e:
        print(f"Expected interrupt occurred: {e}")
    
    print("\n3. Resuming with edited arguments...")
    
    # Resume with edited arguments (change name to 'Robert' and age to 26)
    resume_command = Command(resume=[{
        "type": "edit",
        "args": {
            "args": {
                "cypher_query": "CREATE (p:Person {name: 'Robert', age: 26}) RETURN p",
                "parameters": {}
            }
        }
    }])
    
    try:
        async for chunk in agent.astream(resume_command, config):
            print(f"Resume chunk: {chunk}")
    except Exception as e:
        print(f"Resume completed or error: {e}")


async def main():
    """Run all HIL examples."""
    
    # Check if Neo4j environment variables are set
    required_env_vars = ["NEO4J_URI", "NEO4J_USERNAME", "NEO4J_PASSWORD"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"Warning: Missing environment variables: {missing_vars}")
        print("Some examples may not work without proper Neo4j configuration.")
    
    print("Human-in-the-Loop Examples for LangGraph Tools Agent")
    print("=" * 55)
    
    try:
        # Run examples
        await example_neo4j_hil()
        await example_all_tools_hil()
        await example_edit_tool_call()
        
        print("\n" + "=" * 55)
        print("All examples completed!")
        print("\nKey takeaways:")
        print("- HIL can be configured for specific tool types or all tools")
        print("- Write operations can be separated from read operations")
        print("- Tool calls can be approved, edited, or rejected")
        print("- The agent state is preserved during interrupts")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure the agent dependencies are properly installed and configured.")


if __name__ == "__main__":
    asyncio.run(main())
