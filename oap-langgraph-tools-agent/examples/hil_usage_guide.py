#!/usr/bin/env python3
"""
Human-in-the-Loop Usage Guide for LangGraph Tools Agent

This guide demonstrates the basic patterns for using HIL functionality.
"""

import asyncio
from langgraph.types import Command
from tools_agent.agent import graph


class HILUsageGuide:
    """Guide for using Human-in-the-Loop functionality."""
    
    def __init__(self):
        self.base_config = {
            "configurable": {
                "thread_id": "hil_guide",
                "model_name": "openai:gpt-4.1-nano",
                "temperature": 0.7,
            }
        }
    
    async def basic_hil_setup(self):
        """Basic HIL configuration options."""
        
        print("=== Basic HIL Configuration ===")
        
        # Option 1: HIL for Neo4j write operations only
        config_neo4j_writes = {
            **self.base_config,
            "configurable": {
                **self.base_config["configurable"],
                "graph_database_tools": True,
                "human_in_the_loop": True,
                "hil_neo4j_write_ops": True,
                "hil_all_tools": False,
            }
        }
        
        # Option 2: HIL for all tools
        config_all_tools = {
            **self.base_config,
            "configurable": {
                **self.base_config["configurable"],
                "graph_database_tools": True,
                "human_in_the_loop": True,
                "hil_all_tools": True,
            }
        }
        
        # Option 3: No HIL (default)
        config_no_hil = {
            **self.base_config,
            "configurable": {
                **self.base_config["configurable"],
                "graph_database_tools": True,
                "human_in_the_loop": False,
            }
        }
        
        print("Configuration options:")
        print("1. Neo4j writes only:", config_neo4j_writes["configurable"])
        print("2. All tools:", config_all_tools["configurable"])
        print("3. No HIL:", config_no_hil["configurable"])
        
        return config_neo4j_writes, config_all_tools, config_no_hil
    
    async def handle_interrupt_responses(self):
        """Demonstrate different ways to respond to interrupts."""
        
        print("\n=== Interrupt Response Patterns ===")
        
        # 1. Accept the tool call as-is
        accept_response = Command(resume=[{"type": "accept"}])
        print("1. Accept:", accept_response)
        
        # 2. Edit the tool call arguments
        edit_response = Command(resume=[{
            "type": "edit",
            "args": {
                "args": {
                    "cypher_query": "MATCH (n) RETURN count(n) as total",
                    "parameters": {}
                }
            }
        }])
        print("2. Edit:", edit_response)
        
        # 3. Provide custom response instead of executing tool
        respond_response = Command(resume=[{
            "type": "response",
            "args": "I cannot execute this operation at this time."
        }])
        print("3. Custom response:", respond_response)
        
        # 4. Reject the tool call
        reject_response = Command(resume=[{"type": "reject"}])
        print("4. Reject:", reject_response)
        
        return accept_response, edit_response, respond_response, reject_response
    
    async def workflow_example(self):
        """Complete workflow example."""
        
        print("\n=== Complete Workflow Example ===")
        
        config = {
            **self.base_config,
            "configurable": {
                **self.base_config["configurable"],
                "graph_database_tools": True,
                "human_in_the_loop": True,
                "hil_neo4j_write_ops": True,
                "hil_all_tools": False,
            }
        }
        
        print("1. Create agent with HIL configuration")
        agent = await graph(config)
        
        print("2. Send message that triggers tool call")
        messages = [{"role": "user", "content": "Create a test node in Neo4j"}]
        
        print("3. Start agent execution")
        # In a real application, you would handle the interrupt here
        print("   -> Agent would pause at interrupt")
        print("   -> Present tool call details to human")
        print("   -> Collect human decision")
        
        print("4. Resume based on human decision")
        # Example responses:
        responses = await self.handle_interrupt_responses()
        print("   -> Choose appropriate response type")
        
        print("5. Agent continues execution")
        print("   -> Tool executes with approved/edited parameters")
        print("   -> Agent provides final response")
        
        return agent, config, messages, responses


async def main():
    """Run the usage guide."""
    
    print("Human-in-the-Loop Usage Guide")
    print("=" * 40)
    
    guide = HILUsageGuide()
    
    # Show configuration options
    await guide.basic_hil_setup()
    
    # Show response patterns
    await guide.handle_interrupt_responses()
    
    # Show complete workflow
    await guide.workflow_example()
    
    print("\n" + "=" * 40)
    print("Usage Summary:")
    print("1. Enable HIL in configuration")
    print("2. Run agent normally")
    print("3. Handle interrupts when they occur")
    print("4. Resume with appropriate response")
    print("5. Agent continues execution")
    
    print("\nConfiguration Keys:")
    print("- human_in_the_loop: Enable/disable HIL")
    print("- hil_neo4j_write_ops: HIL for Neo4j writes")
    print("- hil_all_tools: HIL for all tools")
    
    print("\nResponse Types:")
    print("- accept: Execute tool as-is")
    print("- edit: Execute with modified args")
    print("- response: Return custom message")
    print("- reject: Cancel tool execution")


if __name__ == "__main__":
    asyncio.run(main())
