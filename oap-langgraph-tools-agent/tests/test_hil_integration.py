#!/usr/bin/env python3
"""
Integration test for human-in-the-loop functionality with the full agent.

This script tests the complete HIL workflow with the actual agent configuration.
"""

import asyncio
import os
from unittest.mock import patch, AsyncMock
from tools_agent.agent import graph
from langgraph.types import Command


class HILIntegrationTest:
    """Integration tests for HIL with the full agent."""
    
    def __init__(self):
        self.test_results = []
    
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result."""
        status = "PASS" if success else "FAIL"
        self.test_results.append({
            "test": test_name,
            "status": status,
            "message": message
        })
        print(f"[{status}] {test_name}: {message}")
    
    async def test_agent_creation_with_hil(self):
        """Test agent creation with HIL configuration."""
        test_name = "Agent Creation with HIL"
        
        try:
            config = {
                "configurable": {
                    "thread_id": "test_hil_1",
                    "model_name": "openai:gpt-4.1-nano",
                    "human_in_the_loop": True,
                    "hil_neo4j_write_ops": True,
                    "graph_database_tools": True,
                }
            }
            
            agent = await graph(config)
            
            # Check if agent was created successfully
            if agent is not None:
                self.log_result(test_name, True, "Agent created successfully with HIL config")
            else:
                self.log_result(test_name, False, "Agent creation returned None")
                
        except Exception as e:
            self.log_result(test_name, False, f"Exception during agent creation: {str(e)}")
    
    async def test_agent_creation_without_hil(self):
        """Test agent creation without HIL configuration."""
        test_name = "Agent Creation without HIL"
        
        try:
            config = {
                "configurable": {
                    "thread_id": "test_no_hil_1",
                    "model_name": "openai:gpt-4.1-nano",
                    "human_in_the_loop": False,
                    "graph_database_tools": True,
                }
            }
            
            agent = await graph(config)
            
            if agent is not None:
                self.log_result(test_name, True, "Agent created successfully without HIL")
            else:
                self.log_result(test_name, False, "Agent creation returned None")
                
        except Exception as e:
            self.log_result(test_name, False, f"Exception during agent creation: {str(e)}")
    
    async def test_hil_configuration_options(self):
        """Test different HIL configuration combinations."""
        test_name = "HIL Configuration Options"
        
        configs = [
            {
                "name": "HIL Neo4j writes only",
                "config": {
                    "human_in_the_loop": True,
                    "hil_neo4j_write_ops": True,
                    "hil_all_tools": False,
                }
            },
            {
                "name": "HIL all tools",
                "config": {
                    "human_in_the_loop": True,
                    "hil_neo4j_write_ops": True,
                    "hil_all_tools": True,
                }
            },
            {
                "name": "HIL disabled",
                "config": {
                    "human_in_the_loop": False,
                    "hil_neo4j_write_ops": False,
                    "hil_all_tools": False,
                }
            }
        ]
        
        success_count = 0
        for i, config_test in enumerate(configs):
            try:
                config = {
                    "configurable": {
                        "thread_id": f"test_config_{i}",
                        "model_name": "openai:gpt-4.1-nano",
                        "graph_database_tools": True,
                        **config_test["config"]
                    }
                }
                
                agent = await graph(config)
                if agent is not None:
                    success_count += 1
                    print(f"  ✓ {config_test['name']}: OK")
                else:
                    print(f"  ✗ {config_test['name']}: Agent creation failed")
                    
            except Exception as e:
                print(f"  ✗ {config_test['name']}: Exception - {str(e)}")
        
        if success_count == len(configs):
            self.log_result(test_name, True, f"All {len(configs)} configurations tested successfully")
        else:
            self.log_result(test_name, False, f"Only {success_count}/{len(configs)} configurations succeeded")
    
    async def test_neo4j_tools_with_hil(self):
        """Test Neo4j tools integration with HIL."""
        test_name = "Neo4j Tools with HIL"
        
        try:
            # Mock Neo4j environment variables if not set
            env_vars = {
                "NEO4J_URI": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
                "NEO4J_USERNAME": os.getenv("NEO4J_USERNAME", "neo4j"),
                "NEO4J_PASSWORD": os.getenv("NEO4J_PASSWORD", "password"),
                "NEO4J_DATABASE": os.getenv("NEO4J_DATABASE", "neo4j"),
            }
            
            with patch.dict(os.environ, env_vars):
                config = {
                    "configurable": {
                        "thread_id": "test_neo4j_hil",
                        "model_name": "openai:gpt-4.1-nano",
                        "graph_database_tools": True,
                        "human_in_the_loop": True,
                        "hil_neo4j_write_ops": True,
                    }
                }
                
                agent = await graph(config)
                
                if agent is not None:
                    self.log_result(test_name, True, "Neo4j tools with HIL configured successfully")
                else:
                    self.log_result(test_name, False, "Failed to configure Neo4j tools with HIL")
                    
        except Exception as e:
            self.log_result(test_name, False, f"Exception: {str(e)}")
    
    def test_command_objects(self):
        """Test Command object creation for HIL responses."""
        test_name = "Command Objects"
        
        try:
            # Test different command types
            commands = [
                Command(resume=[{"type": "accept"}]),
                Command(resume=[{"type": "edit", "args": {"args": {"param": "value"}}}]),
                Command(resume=[{"type": "response", "args": "User feedback"}]),
                Command(resume=[{"type": "reject"}]),
            ]
            
            # Verify all commands were created successfully
            for i, cmd in enumerate(commands):
                if not hasattr(cmd, 'resume') or not cmd.resume:
                    self.log_result(test_name, False, f"Command {i} creation failed")
                    return
            
            self.log_result(test_name, True, f"All {len(commands)} command types created successfully")
            
        except Exception as e:
            self.log_result(test_name, False, f"Exception creating commands: {str(e)}")
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print("HIL Integration Tests")
        print("=" * 50)
        
        # Run async tests
        await self.test_agent_creation_with_hil()
        await self.test_agent_creation_without_hil()
        await self.test_hil_configuration_options()
        await self.test_neo4j_tools_with_hil()
        
        # Run sync tests
        self.test_command_objects()
        
        # Print summary
        print("\n" + "=" * 50)
        print("Test Summary:")
        
        passed = sum(1 for result in self.test_results if result["status"] == "PASS")
        total = len(self.test_results)
        
        print(f"Passed: {passed}/{total}")
        
        if passed == total:
            print("🎉 All tests passed!")
        else:
            print("❌ Some tests failed. Check the output above for details.")
            
        return passed == total


async def main():
    """Run the integration tests."""
    tester = HILIntegrationTest()
    success = await tester.run_all_tests()
    
    if not success:
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
