#!/usr/bin/env python3
"""
Test script for human-in-the-loop functionality.

This script tests the HIL implementation with various scenarios:
1. Neo4j write operation detection
2. Tool wrapping functionality
3. Different response types
4. Configuration options
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from tools_agent.utils.human_in_the_loop import (
    is_write_operation,
    add_human_in_the_loop,
    add_conditional_hil_to_neo4j_tool,
    create_simple_interrupt_tool,
    HumanInterruptConfig,
    HumanInterrupt,
)
from langchain_core.tools import tool
from langgraph.types import Command


class TestWriteOperationDetection:
    """Test Neo4j write operation detection."""
    
    def test_create_operations(self):
        """Test CREATE operation detection."""
        queries = [
            "CREATE (n:Person {name: 'Alice'})",
            "create (n:Person {name: '<PERSON>'})",
            "MATCH (n) CREATE (m:Person {name: '<PERSON>'})",
        ]
        for query in queries:
            assert is_write_operation(query), f"Should detect CREATE in: {query}"
    
    def test_merge_operations(self):
        """Test MERGE operation detection."""
        queries = [
            "MERGE (n:Person {name: '<PERSON>'})",
            "merge (n:Person {name: '<PERSON>'})",
            "MATCH (n) MERGE (m:Person {name: 'Charlie'})",
        ]
        for query in queries:
            assert is_write_operation(query), f"Should detect MERGE in: {query}"
    
    def test_set_operations(self):
        """Test SET operation detection."""
        queries = [
            "MATCH (n:Person) SET n.age = 30",
            "set n.name = 'Updated'",
            "MATCH (n) SET n += {age: 25}",
        ]
        for query in queries:
            assert is_write_operation(query), f"Should detect SET in: {query}"
    
    def test_delete_operations(self):
        """Test DELETE operation detection."""
        queries = [
            "MATCH (n:Person) DELETE n",
            "delete n",
            "DETACH DELETE n",
            "detach delete n",
        ]
        for query in queries:
            assert is_write_operation(query), f"Should detect DELETE in: {query}"
    
    def test_read_operations(self):
        """Test that read operations are not detected as writes."""
        queries = [
            "MATCH (n) RETURN n",
            "match (n:Person) return n.name",
            "SHOW CONSTRAINTS",
            "CALL db.labels()",
            "EXPLAIN MATCH (n) RETURN count(n)",
            "PROFILE MATCH (n) RETURN n",
        ]
        for query in queries:
            assert not is_write_operation(query), f"Should NOT detect write in: {query}"
    
    def test_edge_cases(self):
        """Test edge cases for write operation detection."""
        # Empty or None queries
        assert not is_write_operation("")
        assert not is_write_operation(None)
        
        # Queries with keywords in strings (should still detect)
        assert is_write_operation("CREATE (n {description: 'This MATCH is in a string'})")
        
        # Queries with keywords as part of other words (should not detect)
        assert not is_write_operation("MATCH (n:CreateUser) RETURN n")


class TestHILWrappers:
    """Test HIL wrapper functionality."""
    
    @pytest.fixture
    def sample_tool(self):
        """Create a sample tool for testing."""
        @tool
        def test_tool(message: str) -> str:
            """A test tool that returns a message."""
            return f"Tool executed with: {message}"
        
        return test_tool
    
    @pytest.fixture
    def neo4j_tool(self):
        """Create a sample Neo4j tool for testing."""
        @tool
        def neo4j_query(cypher_query: str) -> str:
            """Execute a Cypher query."""
            return f"Query executed: {cypher_query}"
        
        return neo4j_query
    
    def test_human_interrupt_config(self):
        """Test HumanInterruptConfig creation."""
        config = HumanInterruptConfig()
        assert config.allow_accept is True
        assert config.allow_edit is True
        assert config.allow_respond is True
        assert config.allow_reject is True
        
        config_custom = HumanInterruptConfig(
            allow_accept=False,
            allow_edit=True,
            allow_respond=False,
            allow_reject=True
        )
        assert config_custom.allow_accept is False
        assert config_custom.allow_edit is True
        assert config_custom.allow_respond is False
        assert config_custom.allow_reject is True
    
    def test_human_interrupt_structure(self):
        """Test HumanInterrupt structure."""
        config = HumanInterruptConfig()
        interrupt = HumanInterrupt(
            action_request={"action": "test", "args": {"param": "value"}},
            config=config,
            description="Test interrupt"
        )
        
        interrupt_dict = interrupt.to_dict()
        assert interrupt_dict["action_request"]["action"] == "test"
        assert interrupt_dict["action_request"]["args"]["param"] == "value"
        assert interrupt_dict["description"] == "Test interrupt"
        assert interrupt_dict["config"]["allow_accept"] is True
    
    @patch('tools_agent.utils.human_in_the_loop.interrupt')
    def test_add_human_in_the_loop_accept(self, mock_interrupt, sample_tool):
        """Test HIL wrapper with accept response."""
        mock_interrupt.return_value = [{"type": "accept"}]
        
        wrapped_tool = add_human_in_the_loop(sample_tool)
        
        # Mock config
        config = Mock()
        
        # Execute the wrapped tool
        result = wrapped_tool.invoke({"message": "test"}, config)
        
        # Verify interrupt was called
        mock_interrupt.assert_called_once()
        
        # Verify result (should be from original tool)
        assert "Tool executed with: test" in result
    
    @patch('tools_agent.utils.human_in_the_loop.interrupt')
    def test_add_human_in_the_loop_edit(self, mock_interrupt, sample_tool):
        """Test HIL wrapper with edit response."""
        mock_interrupt.return_value = [{
            "type": "edit",
            "args": {"args": {"message": "edited_message"}}
        }]
        
        wrapped_tool = add_human_in_the_loop(sample_tool)
        config = Mock()
        
        result = wrapped_tool.invoke({"message": "original"}, config)
        
        mock_interrupt.assert_called_once()
        assert "Tool executed with: edited_message" in result
    
    @patch('tools_agent.utils.human_in_the_loop.interrupt')
    def test_add_human_in_the_loop_response(self, mock_interrupt, sample_tool):
        """Test HIL wrapper with custom response."""
        mock_interrupt.return_value = [{
            "type": "response",
            "args": "Custom user response"
        }]
        
        wrapped_tool = add_human_in_the_loop(sample_tool)
        config = Mock()
        
        result = wrapped_tool.invoke({"message": "test"}, config)
        
        mock_interrupt.assert_called_once()
        assert "Custom user response" in result
    
    @patch('tools_agent.utils.human_in_the_loop.interrupt')
    def test_add_human_in_the_loop_reject(self, mock_interrupt, sample_tool):
        """Test HIL wrapper with reject response."""
        mock_interrupt.return_value = [{"type": "reject"}]
        
        wrapped_tool = add_human_in_the_loop(sample_tool)
        config = Mock()
        
        result = wrapped_tool.invoke({"message": "test"}, config)
        
        mock_interrupt.assert_called_once()
        assert "rejected by user" in result
    
    def test_conditional_hil_neo4j_write(self, neo4j_tool):
        """Test conditional HIL for Neo4j write operations."""
        with patch('tools_agent.utils.human_in_the_loop.interrupt') as mock_interrupt:
            mock_interrupt.return_value = [{"type": "accept"}]
            
            wrapped_tool = add_conditional_hil_to_neo4j_tool(neo4j_tool)
            config = Mock()
            
            # Test with write operation - should interrupt
            result = wrapped_tool.invoke({"cypher_query": "CREATE (n:Person)"}, config)
            mock_interrupt.assert_called_once()
            
            # Reset mock
            mock_interrupt.reset_mock()
            
            # Test with read operation - should not interrupt
            with patch.object(neo4j_tool, 'invoke', return_value="Read result") as mock_invoke:
                result = wrapped_tool.invoke({"cypher_query": "MATCH (n) RETURN n"}, config)
                mock_interrupt.assert_not_called()
                mock_invoke.assert_called_once()


class TestIntegration:
    """Integration tests for HIL functionality."""
    
    def test_command_creation(self):
        """Test Command object creation for different response types."""
        # Accept command
        accept_cmd = Command(resume=[{"type": "accept"}])
        assert accept_cmd.resume == [{"type": "accept"}]
        
        # Edit command
        edit_cmd = Command(resume=[{
            "type": "edit",
            "args": {"args": {"param": "new_value"}}
        }])
        assert edit_cmd.resume[0]["type"] == "edit"
        assert edit_cmd.resume[0]["args"]["args"]["param"] == "new_value"
        
        # Response command
        response_cmd = Command(resume=[{
            "type": "response",
            "args": "User feedback"
        }])
        assert response_cmd.resume[0]["type"] == "response"
        assert response_cmd.resume[0]["args"] == "User feedback"
        
        # Reject command
        reject_cmd = Command(resume=[{"type": "reject"}])
        assert reject_cmd.resume == [{"type": "reject"}]


def run_tests():
    """Run all tests."""
    print("Running Human-in-the-Loop Tests")
    print("=" * 40)
    
    # Run pytest programmatically
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
