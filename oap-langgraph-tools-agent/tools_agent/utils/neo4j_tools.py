import os
from typing import Annotated, List, Dict, Any, Optional
from langchain_core.tools import tool
import json
import aiohttp
import base64


class Neo4jHTTPConnection:
    """Neo4j HTTP REST API connection manager"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def get_connection_info(self) -> Dict[str, str]:
        """Get Neo4j connection information"""
        uri = os.getenv("NEO4J_URI")
        username = os.getenv("NEO4J_USERNAME")
        password = os.getenv("NEO4J_PASSWORD")
        database = os.getenv("NEO4J_DATABASE", "neo4j")  # Default to 'neo4j' if not specified

        if not all([uri, username, password]):
            raise ValueError("Neo4j connection parameters not found in environment variables")

        # Derive HTTP URL from the Neo4j URI
        http_url = self._derive_http_url(uri)

        return {
            "url": http_url,
            "username": username,
            "password": password,
            "database": database
        }

    def _derive_http_url(self, neo4j_uri: str) -> str:
        """Derive HTTP URL from Neo4j URI"""
        if neo4j_uri.startswith("bolt+s://"):
            # Secure bolt connection - use HTTPS
            host_port = neo4j_uri.replace("bolt+s://", "")
            if ":" in host_port:
                host = host_port.split(":")[0]
            else:
                host = host_port
            return f"https://{host}:7473"
        elif neo4j_uri.startswith("bolt://"):
            # Regular bolt connection - use HTTP
            host_port = neo4j_uri.replace("bolt://", "")
            if ":" in host_port:
                host = host_port.split(":")[0]
            else:
                host = host_port
            return f"http://{host}:7474"
        else:
            # Fallback for other URI formats
            raise ValueError(f"Unsupported Neo4j URI format: {neo4j_uri}")


async def execute_cypher_query(query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """Execute a Cypher query via HTTP REST API and return results"""
    connection = Neo4jHTTPConnection()
    conn_info = connection.get_connection_info()

    # Prepare the request
    url = f"{conn_info['url']}/db/{conn_info['database']}/tx/commit"

    # Create basic auth header
    auth_string = f"{conn_info['username']}:{conn_info['password']}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

    headers = {
        "Authorization": f"Basic {auth_b64}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # Prepare the payload
    payload = {
        "statements": [
            {
                "statement": query,
                "parameters": parameters or {}
            }
        ]
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=payload, headers=headers) as response:
            if response.status != 200:
                raise Exception(f"Neo4j HTTP request failed with status {response.status}: {await response.text()}")

            result = await response.json()

            # Check for errors
            if result.get("errors"):
                raise Exception(f"Neo4j query error: {result['errors']}")

            # Extract data from the response
            if result.get("results") and len(result["results"]) > 0:
                result_data = result["results"][0]
                columns = result_data.get("columns", [])
                rows = result_data.get("data", [])

                # Convert to list of dictionaries
                records = []
                for row_data in rows:
                    row_values = row_data.get("row", [])
                    record = {}
                    for i, column in enumerate(columns):
                        if i < len(row_values):
                            record[column] = row_values[i]
                    records.append(record)

                return records

            return []


async def get_database_schema() -> Dict[str, Any]:
    """Get the Neo4j database schema information"""
    schema_queries = {
        "node_labels": "CALL db.labels() YIELD label RETURN collect(label) as labels",
        "relationship_types": "CALL db.relationshipTypes() YIELD relationshipType RETURN collect(relationshipType) as types",
        "property_keys": "CALL db.propertyKeys() YIELD propertyKey RETURN collect(propertyKey) as keys",
        "constraints": "SHOW CONSTRAINTS YIELD name, type, entityType, labelsOrTypes, properties RETURN collect({name: name, type: type, entityType: entityType, labelsOrTypes: labelsOrTypes, properties: properties}) as constraints",
        "indexes": "SHOW INDEXES YIELD name, type, entityType, labelsOrTypes, properties RETURN collect({name: name, type: type, entityType: entityType, labelsOrTypes: labelsOrTypes, properties: properties}) as indexes"
    }
    
    schema = {}
    for key, query in schema_queries.items():
        try:
            result = await execute_cypher_query(query)
            schema[key] = result[0] if result else []
        except Exception as e:
            schema[key] = f"Error retrieving {key}: {str(e)}"
    
    return schema


@tool
async def neo4j_query(
    cypher_query: Annotated[str, "The Cypher query to execute against the Neo4j database"],
    parameters: Annotated[Optional[Dict[str, Any]], "Optional parameters for the Cypher query"] = None
) -> str:
    """
    Execute a Cypher query against the Neo4j database.
    
    This tool allows you to run read and write operations on the Neo4j graph database.
    Use proper Cypher syntax and be careful with write operations.
    
    Examples:
    - MATCH (n) RETURN count(n) as total_nodes
    - MATCH (n:Person) RETURN n.name LIMIT 10
    - CREATE (p:Person {name: 'John', age: 30}) RETURN p
    """
    try:
        results = await execute_cypher_query(cypher_query, parameters)
        
        if not results:
            return "Query executed successfully. No results returned."
        
        # Format results for better readability
        formatted_results = []
        for record in results:
            formatted_results.append(record)
        
        return json.dumps(formatted_results, indent=2, default=str)
    
    except Exception as e:
        return f"Error executing Cypher query: {str(e)}"


@tool
async def neo4j_schema() -> str:
    """
    Get the schema information of the Neo4j database.
    
    This tool returns information about:
    - Node labels
    - Relationship types  
    - Property keys
    - Constraints
    - Indexes
    
    Use this to understand the structure of the graph database before writing queries.
    """
    try:
        schema = await get_database_schema()
        return json.dumps(schema, indent=2, default=str)
    
    except Exception as e:
        return f"Error retrieving database schema: {str(e)}"


@tool
async def neo4j_node_count() -> str:
    """
    Get the total count of nodes in the Neo4j database.
    
    This is a quick way to understand the size of your graph database.
    """
    try:
        result = await execute_cypher_query("MATCH (n) RETURN count(n) as total_nodes")
        total_nodes = result[0]["total_nodes"] if result else 0
        return f"Total nodes in database: {total_nodes}"
    
    except Exception as e:
        return f"Error counting nodes: {str(e)}"


@tool
async def neo4j_relationship_count() -> str:
    """
    Get the total count of relationships in the Neo4j database.
    
    This helps understand the connectivity of your graph database.
    """
    try:
        result = await execute_cypher_query("MATCH ()-[r]->() RETURN count(r) as total_relationships")
        total_relationships = result[0]["total_relationships"] if result else 0
        return f"Total relationships in database: {total_relationships}"
    
    except Exception as e:
        return f"Error counting relationships: {str(e)}"


def get_neo4j_tools():
    """Return all Neo4j tools"""
    return [
        neo4j_query,
        neo4j_schema,
        neo4j_node_count,
        neo4j_relationship_count,
    ]
