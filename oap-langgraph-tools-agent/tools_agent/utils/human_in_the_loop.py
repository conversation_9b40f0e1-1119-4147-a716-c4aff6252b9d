"""
Human-in-the-loop utilities for LangGraph tools agent.

This module provides wrapper functions to add human-in-the-loop functionality
to any tool, allowing for human review and approval before tool execution.
"""

from typing import Callable, Dict, Any, List, Optional, Union
from langchain_core.tools import BaseTool, tool as create_tool
from langchain_core.runnables import RunnableConfig
from langgraph.types import interrupt, Command
import re


class HumanInterruptConfig:
    """Configuration for human interrupt behavior."""
    
    def __init__(
        self,
        allow_accept: bool = True,
        allow_edit: bool = True,
        allow_respond: bool = True,
        allow_reject: bool = True,
    ):
        self.allow_accept = allow_accept
        self.allow_edit = allow_edit
        self.allow_respond = allow_respond
        self.allow_reject = allow_reject


class HumanInterrupt:
    """Human interrupt request structure compatible with Agent Inbox."""
    
    def __init__(
        self,
        action_request: Dict[str, Any],
        config: HumanInterruptConfig,
        description: str = "Please review the tool call",
    ):
        self.action_request = action_request
        self.config = config
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "action_request": self.action_request,
            "config": {
                "allow_accept": self.config.allow_accept,
                "allow_edit": self.config.allow_edit,
                "allow_respond": self.config.allow_respond,
                "allow_reject": self.config.allow_reject,
            },
            "description": self.description,
        }


def is_write_operation(query: str) -> bool:
    """
    Determine if a Cypher query contains write operations.
    
    Args:
        query: The Cypher query string
        
    Returns:
        True if the query contains write operations, False otherwise
    """
    if not query:
        return False
    
    # Convert to uppercase for case-insensitive matching
    query_upper = query.upper().strip()
    
    # Define write operation keywords
    write_keywords = [
        'CREATE', 'MERGE', 'SET', 'DELETE', 'REMOVE', 
        'DROP', 'DETACH DELETE', 'FOREACH'
    ]
    
    # Check if any write keywords are present
    for keyword in write_keywords:
        # Use word boundaries to avoid false positives
        pattern = r'\b' + re.escape(keyword) + r'\b'
        if re.search(pattern, query_upper):
            return True
    
    return False


def add_human_in_the_loop(
    tool: Union[Callable, BaseTool],
    *,
    interrupt_config: Optional[HumanInterruptConfig] = None,
    condition_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
    custom_message: Optional[str] = None,
) -> BaseTool:
    """
    Wrap a tool to support human-in-the-loop review.
    
    Args:
        tool: The tool to wrap (function or BaseTool instance)
        interrupt_config: Configuration for interrupt behavior
        condition_func: Optional function to determine when to interrupt
        custom_message: Custom message for the interrupt
        
    Returns:
        A wrapped tool that supports human-in-the-loop
    """
    if not isinstance(tool, BaseTool):
        tool = create_tool(tool)
    
    if interrupt_config is None:
        interrupt_config = HumanInterruptConfig()
    
    @create_tool(
        tool.name,
        description=tool.description,
        args_schema=tool.args_schema
    )
    def call_tool_with_interrupt(config: RunnableConfig, **tool_input):
        # Check if we should interrupt based on condition
        should_interrupt = True
        if condition_func:
            should_interrupt = condition_func(tool_input)
        
        if not should_interrupt:
            # Execute tool directly without interruption
            return tool.invoke(tool_input, config)
        
        # Create interrupt request
        message = custom_message or f"Please review the {tool.name} tool call"
        request = HumanInterrupt(
            action_request={
                "action": tool.name,
                "args": tool_input
            },
            config=interrupt_config,
            description=message
        )
        
        # Interrupt and wait for human response
        response = interrupt([request.to_dict()])[0]
        
        # Handle different response types
        if response["type"] == "accept":
            # Execute tool with original arguments
            tool_response = tool.invoke(tool_input, config)
        elif response["type"] == "edit":
            # Execute tool with edited arguments
            edited_args = response["args"]["args"]
            tool_response = tool.invoke(edited_args, config)
        elif response["type"] == "response":
            # Return user feedback instead of executing tool
            user_feedback = response["args"]
            tool_response = f"Tool execution cancelled. User feedback: {user_feedback}"
        elif response["type"] == "reject":
            # Reject the tool call
            tool_response = f"Tool execution rejected by user."
        else:
            raise ValueError(f"Unsupported interrupt response type: {response['type']}")
        
        return tool_response
    
    return call_tool_with_interrupt


def add_conditional_hil_to_neo4j_tool(
    tool: Union[Callable, BaseTool],
    *,
    interrupt_config: Optional[HumanInterruptConfig] = None,
) -> BaseTool:
    """
    Add human-in-the-loop to Neo4j tools, but only for write operations.
    
    Args:
        tool: The Neo4j tool to wrap
        interrupt_config: Configuration for interrupt behavior
        
    Returns:
        A wrapped tool that interrupts only for write operations
    """
    def neo4j_condition(tool_input: Dict[str, Any]) -> bool:
        """Check if the Neo4j query is a write operation."""
        cypher_query = tool_input.get("cypher_query", "")
        return is_write_operation(cypher_query)
    
    return add_human_in_the_loop(
        tool,
        interrupt_config=interrupt_config,
        condition_func=neo4j_condition,
        custom_message="This Cypher query contains write operations. Please review before execution."
    )


def create_simple_interrupt_tool(
    tool: Union[Callable, BaseTool],
    message: str = "Please approve this tool call",
) -> BaseTool:
    """
    Create a simple interrupt tool using basic interrupt() function.
    
    This is a simpler alternative to the Agent Inbox compatible version.
    
    Args:
        tool: The tool to wrap
        message: Message to show during interrupt
        
    Returns:
        A wrapped tool with simple interrupt functionality
    """
    if not isinstance(tool, BaseTool):
        tool = create_tool(tool)
    
    @create_tool(
        tool.name,
        description=tool.description,
        args_schema=tool.args_schema
    )
    def simple_interrupt_tool(config: RunnableConfig, **tool_input):
        # Simple interrupt with approval/edit options
        response = interrupt(
            f"{message}\n"
            f"Tool: {tool.name}\n"
            f"Arguments: {tool_input}\n"
            f"Please respond with: {{'type': 'accept'}} to approve, "
            f"{{'type': 'edit', 'args': {{...}}}} to modify arguments, "
            f"or {{'type': 'reject'}} to cancel."
        )
        
        if response["type"] == "accept":
            return tool.invoke(tool_input, config)
        elif response["type"] == "edit":
            edited_args = response["args"]
            return tool.invoke(edited_args, config)
        elif response["type"] == "reject":
            return "Tool execution was rejected by user."
        else:
            raise ValueError(f"Unknown response type: {response['type']}")
    
    return simple_interrupt_tool
