[project]
name = "oap-supervisor"
version = "0.1.0"
description = "LangGraph supervisor agent for Open Agent Platform"
authors = [
    { name = "langchain-ai" }, 
]
requires-python = ">=3.11.0,<3.13"
dependencies = [
    "langgraph>=0.4.7",
    "langchain-core>=0.3.59",
    "langchain-openai>=0.3.16",
    "pydantic==2.11.3",
    "supabase>=2.15.1",
    "aiohttp>=3.8.0",
    "langgraph-supervisor>=0.0.27",
]

[tool.setuptools]
packages = ["oap_supervisor"]

[dependency-groups]
dev = [
    "ruff>=0.8.4",
    "langgraph-api>=0.2.27",
    "langgraph-cli>=0.2.10",
    "langgraph-runtime-inmem>=0.0.11",
    "python-dotenv>=1.0.1",
]
